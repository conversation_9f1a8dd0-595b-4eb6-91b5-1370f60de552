// Product data
const products = [
  {
    id: 1,
    name: "Luminous Foundation",
    price: 39.99,
    category: "face",
    image: "https://images.unsplash.com/photo-1596462502278-27bfdc403348",
  },
  {
    id: 2,
    name: "Velvet Matte Lipstick",
    price: 24.99,
    category: "lips",
    image: "https://images.unsplash.com/photo-1586495777744-4413f21062fa",
  },
  {
    id: 3,
    name: "Smokey Eye Palette",
    price: 45.99,
    category: "eyes",
    image: "https://images.unsplash.com/photo-1583241801142-113b9f5bbde5",
  },
  {
    id: 4,
    name: "Hydrating Face Serum",
    price: 52.99,
    category: "skincare",
    image: "https://images.unsplash.com/photo-1522335789203-aabd1fc54bc9",
  },
  {
    id: 5,
    name: "Volumizing Mascara",
    price: 19.99,
    category: "eyes",
    image: "https://images.unsplash.com/photo-1591360236480-9c6a4cb3a8f0",
  },
  {
    id: 6,
    name: "<PERSON><PERSON>",
    price: 28.99,
    category: "face",
    image: "https://images.unsplash.com/photo-1596462502278-27bfdc403348",
  },
  {
    id: 7,
    name: "Moisturizing Lip Gloss",
    price: 22.99,
    category: "lips",
    image: "https://images.unsplash.com/photo-1586495777744-4413f21062fa",
  },
  {
    id: 8,
    name: "Exfoliating Facial Scrub",
    price: 34.99,
    category: "skincare",
    image: "https://images.unsplash.com/photo-1522335789203-aabd1fc54bc9",
  },
];

// Bestsellers
const bestsellers = products.slice(0, 4);

// DOM Elements
const productGrid = document.querySelector(".product-grid");
const bestsellerSlider = document.querySelector(".bestseller-slider");
const filterBtns = document.querySelectorAll(".filter-btn");
const hamburger = document.querySelector(".hamburger");
const navLinks = document.querySelector(".nav-links");
const cartCount = document.querySelector(".cart-count");

// Initialize cart
let cart = [];

// Display Products
function displayProducts(productsArray) {
  productGrid.innerHTML = "";

  productsArray.forEach((product) => {
    const productCard = document.createElement("div");
    productCard.classList.add("product-card");

    productCard.innerHTML = `
            <div class="product-image">
                <img src="${product.image}" alt="${product.name}">
            </div>
            <div class="product-info">
                <h3>${product.name}</h3>
                <p class="price">$${product.price}</p>
                <button class="add-to-cart" data-id="${product.id}">Add to Cart</button>
            </div>
        `;

    productGrid.appendChild(productCard);
  });

  // Add event listeners to Add to Cart buttons
  document.querySelectorAll(".add-to-cart").forEach((button) => {
    button.addEventListener("click", addToCart);
  });
}

// Display Bestsellers
function displayBestsellers() {
  bestsellers.forEach((product) => {
    const bestsellerCard = document.createElement("div");
    bestsellerCard.classList.add("product-card");

    bestsellerCard.innerHTML = `
            <div class="product-image">
                <img src="${product.image}" alt="${product.name}">
            </div>
            <div class="product-info">
                <h3>${product.name}</h3>
                <p class="price">$${product.price}</p>
                <button class="add-to-cart" data-id="${product.id}">Add to Cart</button>
            </div>
        `;

    bestsellerSlider.appendChild(bestsellerCard);
  });

  // Add event listeners to Add to Cart buttons in bestsellers
  bestsellerSlider.querySelectorAll(".add-to-cart").forEach((button) => {
    button.addEventListener("click", addToCart);
  });
}

// Filter Products
function filterProducts() {
  filterBtns.forEach((btn) => {
    btn.addEventListener("click", () => {
      // Remove active class from all buttons
      filterBtns.forEach((btn) => btn.classList.remove("active"));

      // Add active class to clicked button
      btn.classList.add("active");

      const filter = btn.getAttribute("data-filter");

      if (filter === "all") {
        displayProducts(products);
      } else {
        const filteredProducts = products.filter(
          (product) => product.category === filter
        );
        displayProducts(filteredProducts);
      }
    });
  });
}

// Add to Cart
function addToCart(e) {
  const productId = parseInt(e.target.getAttribute("data-id"));
  const product = products.find((product) => product.id === productId);

  cart.push(product);
  updateCartCount();

  // Animation effect
  e.target.textContent = "Added!";
  setTimeout(() => {
    e.target.textContent = "Add to Cart";
  }, 1500);
}

// Update Cart Count
function updateCartCount() {
  cartCount.textContent = cart.length;
}

// Mobile Menu Toggle
function toggleMobileMenu() {
  hamburger.addEventListener("click", () => {
    navLinks.classList.toggle("active");
    hamburger.classList.toggle("active");
  });
}

// Smooth Scrolling
function smoothScroll() {
  document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
    anchor.addEventListener("click", function (e) {
      e.preventDefault();

      const targetId = this.getAttribute("href");
      const targetElement = document.querySelector(targetId);

      if (targetElement) {
        window.scrollTo({
          top: targetElement.offsetTop - 70,
          behavior: "smooth",
        });

        // Close mobile menu if open
        if (navLinks.classList.contains("active")) {
          navLinks.classList.remove("active");
          hamburger.classList.remove("active");
        }
      }
    });
  });
}

// Initialize functions
document.addEventListener("DOMContentLoaded", () => {
  displayProducts(products);
  displayBestsellers();
  filterProducts();
  toggleMobileMenu();
  smoothScroll();
});
// </augment_code_snippet>
