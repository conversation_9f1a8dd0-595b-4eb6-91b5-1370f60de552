// Product data
const products = [
  {
    id: 1,
    name: "Luminous Foundation",
    price: 39.99,
    category: "face",
    image: "https://images.unsplash.com/photo-1596462502278-27bfdc403348",
  },
  {
    id: 2,
    name: "Velvet Matte Lipstick",
    price: 24.99,
    category: "lips",
    image: "https://images.unsplash.com/photo-1586495777744-4413f21062fa",
  },
  {
    id: 3,
    name: "Smokey Eye Palette",
    price: 45.99,
    category: "eyes",
    image: "https://images.unsplash.com/photo-1583241801142-113b9f5bbde5",
  },
  {
    id: 4,
    name: "Hydrating Face Serum",
    price: 52.99,
    category: "skincare",
    image: "https://images.unsplash.com/photo-1522335789203-aabd1fc54bc9",
  },
  {
    id: 5,
    name: "Volumizing Mascara",
    price: 19.99,
    category: "eyes",
    image: "https://images.unsplash.com/photo-1591360236480-9c6a4cb3a8f0",
  },
  {
    id: 6,
    name: "<PERSON><PERSON>",
    price: 28.99,
    category: "face",
    image: "https://images.unsplash.com/photo-1596462502278-27bfdc403348",
  },
  {
    id: 7,
    name: "Moisturizing Lip Gloss",
    price: 22.99,
    category: "lips",
    image: "https://images.unsplash.com/photo-1586495777744-4413f21062fa",
  },
  {
    id: 8,
    name: "Exfoliating Facial Scrub",
    price: 34.99,
    category: "skincare",
    image: "https://images.unsplash.com/photo-1522335789203-aabd1fc54bc9",
  },
];

// Bestsellers
const bestsellers = products.slice(0, 4);

// DOM Elements
const productGrid = document.querySelector(".product-grid");
const bestsellerSlider = document.querySelector(".bestseller-slider");
const filterBtns = document.querySelectorAll(".filter-btn");
const hamburger = document.querySelector(".hamburger");
const navLinks = document.querySelector(".nav-links");
const cartCount = document.querySelector(".cart-count");
const cartIcon = document.getElementById("cart-icon");
const cartModal = document.getElementById("cart-modal");
const closeCart = document.querySelector(".close-cart");
const cartItems = document.getElementById("cart-items");
const cartTotal = document.getElementById("cart-total");
const clearCartBtn = document.querySelector(".clear-cart");
const checkoutBtn = document.querySelector(".checkout-btn");

// Initialize cart
let cart = [];

// Display Products
function displayProducts(productsArray) {
  productGrid.innerHTML = "";

  productsArray.forEach((product) => {
    const productCard = document.createElement("div");
    productCard.classList.add("product-card");

    productCard.innerHTML = `
            <div class="product-image">
                <img src="${product.image}" alt="${product.name}">
            </div>
            <div class="product-info">
                <h3>${product.name}</h3>
                <p class="price">$${product.price}</p>
                <button class="add-to-cart" data-id="${product.id}">Add to Cart</button>
            </div>
        `;

    productGrid.appendChild(productCard);
  });

  // Add event listeners to Add to Cart buttons
  document.querySelectorAll(".add-to-cart").forEach((button) => {
    button.addEventListener("click", addToCart);
  });
}

// Display Bestsellers
function displayBestsellers() {
  bestsellers.forEach((product) => {
    const bestsellerCard = document.createElement("div");
    bestsellerCard.classList.add("product-card");

    bestsellerCard.innerHTML = `
            <div class="product-image">
                <img src="${product.image}" alt="${product.name}">
            </div>
            <div class="product-info">
                <h3>${product.name}</h3>
                <p class="price">$${product.price}</p>
                <button class="add-to-cart" data-id="${product.id}">Add to Cart</button>
            </div>
        `;

    bestsellerSlider.appendChild(bestsellerCard);
  });

  // Add event listeners to Add to Cart buttons in bestsellers
  bestsellerSlider.querySelectorAll(".add-to-cart").forEach((button) => {
    button.addEventListener("click", addToCart);
  });
}

// Filter Products
function filterProducts() {
  filterBtns.forEach((btn) => {
    btn.addEventListener("click", () => {
      // Remove active class from all buttons
      filterBtns.forEach((btn) => btn.classList.remove("active"));

      // Add active class to clicked button
      btn.classList.add("active");

      const filter = btn.getAttribute("data-filter");

      if (filter === "all") {
        displayProducts(products);
      } else {
        const filteredProducts = products.filter(
          (product) => product.category === filter
        );
        displayProducts(filteredProducts);
      }
    });
  });
}

// Add to Cart
function addToCart(e) {
  const productId = parseInt(e.target.getAttribute("data-id"));
  const product = products.find((product) => product.id === productId);

  // Check if product already exists in cart
  const existingItem = cart.find((item) => item.id === productId);

  if (existingItem) {
    existingItem.quantity += 1;
  } else {
    cart.push({ ...product, quantity: 1 });
  }

  updateCartCount();
  updateCartTotal();

  // Animation effect
  e.target.textContent = "Added!";
  setTimeout(() => {
    e.target.textContent = "Add to Cart";
  }, 1500);
}

// Update Cart Count
function updateCartCount() {
  const totalItems = cart.reduce((total, item) => total + item.quantity, 0);
  cartCount.textContent = totalItems;
}

// Update Cart Total
function updateCartTotal() {
  const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  cartTotal.textContent = total.toFixed(2);
}

// Display Cart Items
function displayCartItems() {
  cartItems.innerHTML = "";

  if (cart.length === 0) {
    cartItems.innerHTML = `
      <div class="empty-cart">
        <i class="fas fa-shopping-bag"></i>
        <p>Your cart is empty</p>
      </div>
    `;
    return;
  }

  cart.forEach((item) => {
    const cartItem = document.createElement("div");
    cartItem.classList.add("cart-item");

    cartItem.innerHTML = `
      <div class="cart-item-image">
        <img src="${item.image}" alt="${item.name}">
      </div>
      <div class="cart-item-details">
        <div class="cart-item-name">${item.name}</div>
        <div class="cart-item-price">$${item.price}</div>
        <div class="cart-item-quantity">
          <button class="quantity-btn decrease" data-id="${item.id}">-</button>
          <span class="quantity-display">${item.quantity}</span>
          <button class="quantity-btn increase" data-id="${item.id}">+</button>
        </div>
      </div>
      <button class="remove-item" data-id="${item.id}">Remove</button>
    `;

    cartItems.appendChild(cartItem);
  });

  // Add event listeners for quantity buttons and remove buttons
  addCartEventListeners();
}

// Add Event Listeners for Cart Items
function addCartEventListeners() {
  // Quantity increase buttons
  document.querySelectorAll(".quantity-btn.increase").forEach((btn) => {
    btn.addEventListener("click", (e) => {
      const productId = parseInt(e.target.getAttribute("data-id"));
      const item = cart.find((item) => item.id === productId);
      if (item) {
        item.quantity += 1;
        updateCartCount();
        updateCartTotal();
        displayCartItems();
      }
    });
  });

  // Quantity decrease buttons
  document.querySelectorAll(".quantity-btn.decrease").forEach((btn) => {
    btn.addEventListener("click", (e) => {
      const productId = parseInt(e.target.getAttribute("data-id"));
      const item = cart.find((item) => item.id === productId);
      if (item && item.quantity > 1) {
        item.quantity -= 1;
        updateCartCount();
        updateCartTotal();
        displayCartItems();
      }
    });
  });

  // Remove item buttons
  document.querySelectorAll(".remove-item").forEach((btn) => {
    btn.addEventListener("click", (e) => {
      const productId = parseInt(e.target.getAttribute("data-id"));
      cart = cart.filter((item) => item.id !== productId);
      updateCartCount();
      updateCartTotal();
      displayCartItems();
    });
  });
}

// Show Cart Modal
function showCart() {
  displayCartItems();
  cartModal.style.display = "block";
}

// Hide Cart Modal
function hideCart() {
  cartModal.style.display = "none";
}

// Clear Cart
function clearCart() {
  cart = [];
  updateCartCount();
  updateCartTotal();
  displayCartItems();
}

// Checkout Function
function checkout() {
  if (cart.length === 0) {
    alert("Your cart is empty!");
    return;
  }

  const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const itemCount = cart.reduce((total, item) => total + item.quantity, 0);

  alert(`Thank you for your purchase!\n\nItems: ${itemCount}\nTotal: $${total.toFixed(2)}\n\nThis is a demo checkout.`);

  // Clear cart after checkout
  clearCart();
  hideCart();
}

// Mobile Menu Toggle
function toggleMobileMenu() {
  hamburger.addEventListener("click", () => {
    navLinks.classList.toggle("active");
    hamburger.classList.toggle("active");
  });
}

// Smooth Scrolling
function smoothScroll() {
  document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
    anchor.addEventListener("click", function (e) {
      e.preventDefault();

      const targetId = this.getAttribute("href");
      const targetElement = document.querySelector(targetId);

      if (targetElement) {
        window.scrollTo({
          top: targetElement.offsetTop - 70,
          behavior: "smooth",
        });

        // Close mobile menu if open
        if (navLinks.classList.contains("active")) {
          navLinks.classList.remove("active");
          hamburger.classList.remove("active");
        }
      }
    });
  });
}

// Initialize Cart Event Listeners
function initializeCartEvents() {
  // Cart icon click
  cartIcon.addEventListener("click", showCart);

  // Close cart modal
  closeCart.addEventListener("click", hideCart);

  // Clear cart button
  clearCartBtn.addEventListener("click", clearCart);

  // Checkout button
  checkoutBtn.addEventListener("click", checkout);

  // Close modal when clicking outside
  window.addEventListener("click", (e) => {
    if (e.target === cartModal) {
      hideCart();
    }
  });
}

// Initialize functions
document.addEventListener("DOMContentLoaded", () => {
  displayProducts(products);
  displayBestsellers();
  filterProducts();
  toggleMobileMenu();
  smoothScroll();
  initializeCartEvents();
});
// </augment_code_snippet>
