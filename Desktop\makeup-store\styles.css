/* Base Styles */
:root {
  --primary-color: #ff6b6b;
  --secondary-color: #f9f9f9;
  --dark-color: #333;
  --light-color: #fff;
  --gray-color: #f4f4f4;
  --text-color: #555;
  --transition: all 0.3s ease;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Poppins", sans-serif;
  line-height: 1.6;
  color: var(--text-color);
}

.container {
  width: 90%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

a {
  text-decoration: none;
  color: inherit;
}

ul {
  list-style: none;
}

img {
  max-width: 100%;
  height: auto;
}

.btn {
  display: inline-block;
  background-color: var(--primary-color);
  color: var(--light-color);
  padding: 12px 30px;
  border: none;
  border-radius: 30px;
  cursor: pointer;
  transition: var(--transition);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 14px;
  letter-spacing: 1px;
}

.btn:hover {
  background-color: #ff5252;
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(255, 107, 107, 0.2);
}

.section-title {
  text-align: center;
  margin-bottom: 50px;
  font-size: 32px;
  color: var(--dark-color);
  position: relative;
  padding-bottom: 15px;
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 70px;
  height: 3px;
  background-color: var(--primary-color);
}

/* Header */
header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: var(--light-color);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 15px 0;
}

header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo h1 {
  font-size: 28px;
  font-weight: 700;
  color: var(--dark-color);
}

.logo span {
  color: var(--primary-color);
}

nav {
  display: flex;
  align-items: center;
}

.nav-links {
  display: flex;
}

.nav-links li {
  margin-left: 30px;
}

.nav-links a {
  font-weight: 500;
  transition: var(--transition);
}

.nav-links a:hover {
  color: var(--primary-color);
}

.cart-icon {
  margin-left: 30px;
  position: relative;
  cursor: pointer;
}

.cart-icon i {
  font-size: 20px;
}

.cart-count {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: var(--primary-color);
  color: var(--light-color);
  font-size: 12px;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.hamburger {
  display: none;
  flex-direction: column;
  cursor: pointer;
}

.hamburger span {
  width: 25px;
  height: 3px;
  background-color: var(--dark-color);
  margin: 2px 0;
  transition: var(--transition);
}

/* Hero Section */
.hero {
  height: 100vh;
  background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)),
    url("https://images.unsplash.com/photo-1522335789203-aabd1fc54bc9")
      no-repeat center center/cover;
  display: flex;
  align-items: center;
  text-align: center;
  color: var(--light-color);
  margin-top: 70px;
}

.hero-content h1 {
  font-size: 48px;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-content p {
  font-size: 18px;
  margin-bottom: 30px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Featured Categories */
.featured {
  padding: 80px 0;
  background-color: var(--secondary-color);
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
}

.category-card {
  background-color: var(--light-color);
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: var(--transition);
  text-align: center;
}

.category-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.category-card img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.category-card h3 {
  padding: 20px;
  font-size: 18px;
  color: var(--dark-color);
}

/* Products Section */
.products {
  padding: 80px 0;
}

.filter-bar {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

.filter-btn {
  background: none;
  border: none;
  padding: 10px 20px;
  margin: 0 5px 10px;
  cursor: pointer;
  font-weight: 500;
  transition: var(--transition);
  border-radius: 30px;
}

.filter-btn.active,
.filter-btn:hover {
  background-color: var(--primary-color);
  color: var(--light-color);
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 30px;
}

.product-card {
  background-color: var(--light-color);
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: var(--transition);
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.product-image {
  position: relative;
  overflow: hidden;
  height: 250px;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.product-card:hover .product-image img {
  transform: scale(1.1);
}

.product-info {
  padding: 20px;
}

.product-info h3 {
  font-size: 18px;
  margin-bottom: 10px;
  color: var(--dark-color);
}

.product-info .price {
  font-weight: 600;
  color: var(--primary-color);
  font-size: 18px;
  margin-bottom: 15px;
}

.add-to-cart {
  width: 100%;
  padding: 10px;
  background-color: var(--primary-color);
  color: var(--light-color);
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: var(--transition);
  font-weight: 500;
}

.add-to-cart:hover {
  background-color: #ff5252;
}

/* Cart Modal */
.cart-modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.cart-modal-content {
  background-color: var(--light-color);
  margin: 5% auto;
  padding: 0;
  border-radius: 10px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
  background-color: var(--primary-color);
  color: var(--light-color);
  border-radius: 10px 10px 0 0;
}

.cart-header h2 {
  margin: 0;
  font-size: 24px;
}

.close-cart {
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
  color: var(--light-color);
  transition: var(--transition);
}

.close-cart:hover {
  opacity: 0.7;
}

.cart-items {
  padding: 20px;
  min-height: 200px;
}

.cart-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #eee;
}

.cart-item:last-child {
  border-bottom: none;
}

.cart-item-image {
  width: 60px;
  height: 60px;
  margin-right: 15px;
}

.cart-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 5px;
}

.cart-item-details {
  flex: 1;
}

.cart-item-name {
  font-weight: 600;
  margin-bottom: 5px;
  color: var(--dark-color);
}

.cart-item-price {
  color: var(--primary-color);
  font-weight: 500;
}

.cart-item-quantity {
  display: flex;
  align-items: center;
  margin: 10px 0;
}

.quantity-btn {
  background-color: var(--primary-color);
  color: var(--light-color);
  border: none;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
}

.quantity-btn:hover {
  background-color: #ff5252;
}

.quantity-display {
  margin: 0 15px;
  font-weight: 600;
  min-width: 20px;
  text-align: center;
}

.remove-item {
  background-color: #dc3545;
  color: var(--light-color);
  border: none;
  padding: 5px 10px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 12px;
  transition: var(--transition);
}

.remove-item:hover {
  background-color: #c82333;
}

.cart-footer {
  padding: 20px;
  border-top: 1px solid #eee;
  background-color: #f8f9fa;
  border-radius: 0 0 10px 10px;
}

.cart-total {
  text-align: center;
  margin-bottom: 20px;
  font-size: 20px;
  color: var(--dark-color);
}

.cart-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.cart-actions .btn {
  flex: 1;
  max-width: 150px;
}

.clear-cart {
  background-color: #6c757d;
}

.clear-cart:hover {
  background-color: #5a6268;
}

.checkout-btn {
  background-color: var(--primary-color);
}

.checkout-btn:hover {
  background-color: #ff5252;
}

.empty-cart {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.empty-cart i {
  font-size: 48px;
  margin-bottom: 15px;
  color: #ccc;
}

/* Bestsellers Section */
.bestsellers {
  padding: 80px 0;
  background-color: var(--secondary-color);
}

.bestseller-slider {
  display: flex;
  overflow-x: auto;
  gap: 30px;
  padding: 20px 0;
  scrollbar-width: none;
}

.bestseller-slider::-webkit-scrollbar {
  display: none;
}

/* About Section */
.about {
  padding: 80px 0;
}

.about .container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 50px;
  align-items: center;
}

.about-content {
  text-align: left;
}

.about-content .section-title {
  text-align: left;
}

.about-content .section-title::after {
  left: 0;
  transform: none;
}

.about-content p {
  margin-bottom: 20px;
  font-size: 16px;
}

.about-image img {
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Contact Section */
.contact {
  padding: 80px 0;
  background-color: var(--secondary-color);
}

.contact-wrapper {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 50px;
}

.contact-info {
  background-color: var(--primary-color);
  padding: 40px;
  border-radius: 10px;
  color: var(--light-color);
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
}

.info-item i {
  font-size: 24px;
  margin-right: 20px;
}

.social-links {
  display: flex;
  margin-top: 40px;
}

.social-links a {
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
  transition: var(--transition);
}

.social-links a:hover {
  background-color: var(--light-color);
  color: var(--primary-color);
}

.contact-form {
  background-color: var(--light-color);
  padding: 40px;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 20px;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-family: inherit;
  font-size: 16px;
}

.form-group textarea {
  height: 150px;
  resize: none;
}

/* Footer */
footer {
  background-color: var(--dark-color);
  color: var(--light-color);
  padding: 60px 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
  margin-bottom: 40px;
}

.footer-logo h2 {
  font-size: 24px;
  margin-bottom: 15px;
}

.footer-logo span {
  color: var(--primary-color);
}

.footer-links h3,
.footer-newsletter h3 {
  font-size: 18px;
  margin-bottom: 20px;
  position: relative;
  padding-bottom: 10px;
}

.footer-links h3::after,
.footer-newsletter h3::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 2px;
  background-color: var(--primary-color);
}

.footer-links ul li {
  margin-bottom: 10px;
}

.footer-links ul li a {
  transition: var(--transition);
}

.footer-links ul li a:hover {
  color: var(--primary-color);
  padding-left: 5px;
}

.footer-newsletter form {
  display: flex;
}

.footer-newsletter input {
  flex: 1;
  padding: 10px 15px;
  border: none;
  border-radius: 5px 0 0 5px;
}

.footer-newsletter button {
  background-color: var(--primary-color);
  color: var(--light-color);
  border: none;
  padding: 0 20px;
  border-radius: 0 5px 5px 0;
  cursor: pointer;
  transition: var(--transition);
}

.footer-newsletter button:hover {
  background-color: #ff5252;
}

.footer-bottom {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .category-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .about .container,
  .contact-wrapper,
  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .about-content {
    order: 2;
  }

  .about-image {
    order: 1;
  }
}

@media (max-width: 768px) {
  .nav-links {
    position: fixed;
    top: 70px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 70px);
    background-color: var(--light-color);
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
  }

  .nav-links.active {
    left: 0;
  }

  .nav-links li {
    margin: 15px 0;
  }

  .hamburger {
    display: flex;
    margin-left: 20px;
  }

  .hero-content h1 {
    font-size: 36px;
  }

  .hero-content p {
    font-size: 16px;
  }
}

@media (max-width: 576px) {
  .category-grid {
    grid-template-columns: 1fr;
  }

  .product-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }

  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .footer-links h3::after,
  .footer-newsletter h3::after {
    left: 50%;
    transform: translateX(-50%);
  }

  .footer-newsletter form {
    flex-direction: column;
  }

  .footer-newsletter input {
    border-radius: 5px;
    margin-bottom: 10px;
  }

  .footer-newsletter button {
    border-radius: 5px;
    padding: 10px;
  }
}
